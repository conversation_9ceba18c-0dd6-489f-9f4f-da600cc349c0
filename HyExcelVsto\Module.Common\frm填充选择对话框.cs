using ET;
using Microsoft.Office.Interop.Excel;
using System;
using System.Windows.Forms;

namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// 填充选择对话框，用于选择不同的填充模式
    /// </summary>
    /// <remarks>
    /// 此对话框提供三种填充模式：
    /// 1. 填充当前单元格下方单元格 - 从选定单元格开始向下填充
    /// 2. 填充筛选行下方单元格 - 从筛选行下方开始填充
    /// 3. 填充选定单元格 - 仅填充用户选定的范围
    /// </remarks>
    public partial class frm填充选择对话框 : Form
    {
        /// <summary>
        /// 用户选择的填充模式
        /// </summary>
        public FillMode SelectedFillMode { get; private set; } = FillMode.Cancel;

        /// <summary>
        /// 当前选定的单元格范围
        /// </summary>
        private Range _selectedRange;

        /// <summary>
        /// 筛选行号
        /// </summary>
        private int _filterRowNumber;

        /// <summary>
        /// 填充模式枚举
        /// </summary>
        public enum FillMode
        {
            /// <summary>
            /// 取消操作
            /// </summary>
            Cancel,
            /// <summary>
            /// 填充当前单元格下方单元格
            /// </summary>
            FillBelowCurrentCell,
            /// <summary>
            /// 填充筛选行下方单元格
            /// </summary>
            FillBelowFilterRow,
            /// <summary>
            /// 填充选定单元格
            /// </summary>
            FillSelectedCells
        }

        /// <summary>
        /// 初始化填充选择对话框
        /// </summary>
        public frm填充选择对话框()
        {
            InitializeComponent();
            InitializeDialog();
        }

        /// <summary>
        /// 初始化对话框内容
        /// </summary>
        private void InitializeDialog()
        {
            try
            {
                // 获取当前选定范围
                _selectedRange = ETExcelExtensions.GetSelectionRange();
                if (_selectedRange == null)
                {
                    MessageBox.Show("请先选择要填充的单元格范围", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    DialogResult = DialogResult.Cancel;
                    return;
                }

                // 获取筛选行号
                _filterRowNumber = _selectedRange.Worksheet.GetWorksheetFilterRowNumber();

                // 更新选项描述
                UpdateOptionDescriptions();
            }
            catch (Exception ex)
            {
                ETLogManager.Error("初始化填充选择对话框失败", ex);
                MessageBox.Show("初始化对话框失败，请重试", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                DialogResult = DialogResult.Cancel;
            }
        }

        /// <summary>
        /// 更新选项描述文本
        /// </summary>
        private void UpdateOptionDescriptions()
        {
            if (_selectedRange == null) return;

            // 获取选定范围的第一个单元格
            Range firstCell = _selectedRange.Cells[1, 1];
            string cellAddress = firstCell.Address[false, false];

            // 更新选项1描述
            radioButton填充当前单元格下方.Text = $"填充当前单元格下方单元格 (从 {cellAddress} 开始)";

            // 更新选项2描述
            if (_filterRowNumber > 0)
            {
                radioButton填充筛选行下方.Text = $"填充筛选行下方单元格 (跳过第1-{_filterRowNumber}行)";
                radioButton填充筛选行下方.Enabled = true;
            }
            else
            {
                radioButton填充筛选行下方.Text = "填充筛选行下方单元格 (当前无筛选)";
                radioButton填充筛选行下方.Enabled = false;
            }

            // 更新选项3描述
            radioButton填充选定单元格.Text = $"填充选定单元格 ({_selectedRange.Address[false, false]})";

            // 默认选择第一个可用选项
            if (radioButton填充筛选行下方.Enabled)
            {
                radioButton填充筛选行下方.Checked = true;
            }
            else
            {
                radioButton填充当前单元格下方.Checked = true;
            }
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void button确定_Click(object sender, EventArgs e)
        {
            try
            {
                // 根据选择的单选按钮确定填充模式
                if (radioButton填充当前单元格下方.Checked)
                {
                    SelectedFillMode = FillMode.FillBelowCurrentCell;
                }
                else if (radioButton填充筛选行下方.Checked)
                {
                    SelectedFillMode = FillMode.FillBelowFilterRow;
                }
                else if (radioButton填充选定单元格.Checked)
                {
                    SelectedFillMode = FillMode.FillSelectedCells;
                }
                else
                {
                    MessageBox.Show("请选择一种填充模式", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                DialogResult = DialogResult.OK;
                Close();
            }
            catch (Exception ex)
            {
                ETLogManager.Error("确定填充模式失败", ex);
                MessageBox.Show("操作失败，请重试", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void button取消_Click(object sender, EventArgs e)
        {
            SelectedFillMode = FillMode.Cancel;
            DialogResult = DialogResult.Cancel;
            Close();
        }

        /// <summary>
        /// 获取根据选择模式计算的目标填充范围
        /// </summary>
        /// <returns>目标填充范围</returns>
        public Range GetTargetRange()
        {
            if (_selectedRange == null) return null;

            try
            {
                switch (SelectedFillMode)
                {
                    case FillMode.FillBelowCurrentCell:
                        // 填充当前单元格下方单元格：从选定单元格开始到列底部
                        Range firstCell = _selectedRange.Cells[1, 1];
                        // 获取整列，但从选定单元格行开始
                        Range columnRange = firstCell.EntireColumn;
                        Range currentCellTargetRange = _selectedRange.Worksheet.Range[
                            _selectedRange.Worksheet.Cells[firstCell.Row, firstCell.Column],
                            _selectedRange.Worksheet.Cells[columnRange.Rows.Count, firstCell.Column + _selectedRange.Columns.Count - 1]];
                        return currentCellTargetRange.OptimizeRangeSize();

                    case FillMode.FillBelowFilterRow:
                        // 填充筛选行下方单元格：从筛选行下方开始
                        if (_filterRowNumber > 0)
                        {
                            // 从筛选行的下一行开始，保持选定范围的列数
                            Range filterRowTargetRange = _selectedRange.Worksheet.Range[
                                _selectedRange.Worksheet.Cells[_filterRowNumber + 1, _selectedRange.Column],
                                _selectedRange.Worksheet.Cells[_selectedRange.Worksheet.UsedRange.Rows.Count, _selectedRange.Column + _selectedRange.Columns.Count - 1]];
                            return filterRowTargetRange.OptimizeRangeSize();
                        }
                        return _selectedRange.OptimizeRangeSize();

                    case FillMode.FillSelectedCells:
                        // 填充选定单元格：仅填充用户选定的范围
                        return _selectedRange.OptimizeRangeSize();

                    default:
                        return null;
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error("计算目标填充范围失败", ex);
                return null;
            }
        }
    }
}
