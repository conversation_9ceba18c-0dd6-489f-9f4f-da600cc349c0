﻿namespace HyExcelVsto.Module.Common
{
    partial class frm向下填充
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.checkBox标色 = new System.Windows.Forms.CheckBox();
            this.button按上一行的值填充 = new System.Windows.Forms.Button();
            this.SuspendLayout();
            //
            // checkBox标色
            //
            this.checkBox标色.AutoSize = true;
            this.checkBox标色.Location = new System.Drawing.Point(12, 50);
            this.checkBox标色.Name = "checkBox标色";
            this.checkBox标色.Size = new System.Drawing.Size(96, 16);
            this.checkBox标色.TabIndex = 1;
            this.checkBox标色.Text = "标色(紫罗兰)";
            this.checkBox标色.UseVisualStyleBackColor = true;
            //
            // button按上一行的值填充
            //
            this.button按上一行的值填充.Location = new System.Drawing.Point(12, 12);
            this.button按上一行的值填充.Name = "button按上一行的值填充";
            this.button按上一行的值填充.Size = new System.Drawing.Size(200, 32);
            this.button按上一行的值填充.TabIndex = 0;
            this.button按上一行的值填充.Text = "按上一行的值 填充空白";
            this.button按上一行的值填充.UseVisualStyleBackColor = true;
            this.button按上一行的值填充.Click += new System.EventHandler(this.button按上一行的值填充_Click);
            //
            // frm向下填充
            //
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(224, 78);
            this.Controls.Add(this.checkBox标色);
            this.Controls.Add(this.button按上一行的值填充);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "frm向下填充";
            this.ShowInTaskbar = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "向下填充";
            this.Load += new System.EventHandler(this.frm向下填充_Load);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion
        private System.Windows.Forms.CheckBox checkBox标色;
        private System.Windows.Forms.Button button按上一行的值填充;
    }
}