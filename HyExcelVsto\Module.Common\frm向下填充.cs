using ET;
using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;
using System.Windows.Forms;


namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// 向下填充窗体类，用于处理Excel中的向下填充操作
    /// </summary>
    /// <remarks>
    /// 此窗体提供以下功能：
    /// 1. 按上一行的值进行向下填充
    /// 2. 支持多列同时填充
    /// 3. 可选择是否对填充的单元格进行标色
    /// 4. 支持窗体大小的切换
    /// </remarks>
    public partial class frm向下填充 : Form
    {
        /// <summary>
        /// 初始化向下填充窗体
        /// </summary>
        public frm向下填充()
        {
            InitializeComponent();
            button填充.Visible = false;
            button放大.Visible = false;
            button候选项.Visible = false;
        }

        /// <summary>
        /// 按上一行的值填充按钮点击事件处理
        /// </summary>
        void button按上一行的值填充_Click(object sender, EventArgs e)
        {
            button填充_Click(sender, e);
        }

        /// <summary>
        /// 执行填充操作的按钮点击事件处理
        /// </summary>
        /// <remarks>
        /// 此方法执行以下操作：
        /// 1. 获取选中的单元格区域
        /// 2. 根据选项决定是否填充整列
        /// 3. 处理合并单元格的填充
        /// 4. 可选择性地为填充的单元格添加标色
        /// </remarks>
        void button填充_Click(object sender, EventArgs e)
        {
            List<Range> filledCells = []; // 存放已填充单元格的Range列表

            try
            {
                Range selectedRange = ETExcelExtensions.GetSelectionRange();
                if (selectedRange == null) return;

                // 根据选项决定是否填充整列
                Range targetRange = radioButton填充下方所有单元格.Checked
                    ? selectedRange.EntireColumn
                    : selectedRange;

                targetRange = targetRange.OptimizeRangeSize();
                if (targetRange == null) return;

                ETLogManager.Info($"开始向下填充操作，选中区域: {targetRange.Address}");
                ETExcelExtensions.SetAppFastMode();

                // 遍历每一列进行填充
                for (int columnIndex = 1; columnIndex <= targetRange.Columns.Count; columnIndex++)
                {
                    dynamic currentColumn = targetRange.Columns[columnIndex];
                    object previousValue = null; // 存储上一个非空值
                    bool isMergedCell = false; // 标记是否在处理合并单元格
                    int lastFilledRow = 1; // 最后一个填充值的行索引

                    foreach (Range cell in currentColumn.Cells)
                    {
                        if (cell.EntireRow.Hidden) continue; // 跳过隐藏行

                        if (!cell.IsCellEmpty())
                        {
                            previousValue = cell.Value;
                            isMergedCell = true;
                            lastFilledRow = cell.Row;
                        }
                        else if (cell.IsCellEmpty() && isMergedCell)
                        {
                            cell.Value = previousValue ?? string.Empty;
                            filledCells.Add(cell);
                            ETLogManager.Debug($"填充单元格 {cell.Address} 的值为: {previousValue}");
                        }
                        else if (!cell.IsCellEmpty() && cell.Row != lastFilledRow + 1)
                        {
                            isMergedCell = false;
                            previousValue = string.Empty;
                        }
                    }
                }

                // 如果需要标色且有填充的单元格
                if (checkBox标色.Checked && filledCells.Count > 0)
                {
                    Range filledRange = ETExcelExtensions.UnionRanges(filledCells);
                    filledRange.Format条件格式警示色(EnumWarningColor.紫罗兰);
                    ETLogManager.Info($"已为填充的单元格添加标色，范围: {filledRange.Address}");
                }
            }
            catch (Exception ex)
            {
                throw new ETException("向下填充操作失败", "单元格填充操作", ex);
            }
            finally
            {
                ETExcelExtensions.SetAppNormalMode(true);
            }

            if (!checkBox不关闭.Checked) Close();
        }

        /// <summary>
        /// 处理窗体大小切换的按钮点击事件
        /// </summary>
        /// <remarks>
        /// 控制窗体的显示状态：
        /// - 切换按钮的可见性
        /// - 调整窗体大小
        /// - 调整窗体位置
        /// </remarks>
        void button放大缩小_Click(object sender, EventArgs e)
        {
            button填充.Visible = !button填充.Visible;
            bool isMaximized = !button填充.Visible;

            // 更新按钮可见性
            button缩小.Visible = isMaximized;
            button按上一行的值填充.Visible = isMaximized;
            button放大.Visible = !isMaximized;
            button填充.Visible = !isMaximized;
            button候选项.Visible = !isMaximized;

            // 调整窗体大小
            Height = isMaximized ? 137 : 73;
            Width = isMaximized ? 293 : 173;

            // 调整窗体位置（仅在最小化时）
            if (!isMaximized)
            {
                Left = Convert.ToInt16(Screen.PrimaryScreen.Bounds.Width / 4 * 3);
                Top = 100;
            }
        }

        /// <summary>
        /// 处理候选项按钮点击事件
        /// </summary>
        void button候选项_Click(object sender, EventArgs e)
        {
            frm设置标签及下拉候选项 settingsForm = new();
            settingsForm.设置隐藏下拉项();
            ThisAddIn.DropdownInputForm.PreEntireColumnAddress = string.Empty;
            settingsForm.Close();
        }

        /// <summary>
        /// 窗体加载事件处理
        /// </summary>
        void frm向下填充_Load(object sender, EventArgs e)
        {
            button放大缩小_Click(null, null);
        }
    }
}