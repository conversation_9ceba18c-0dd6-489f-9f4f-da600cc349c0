using ET;
using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;
using System.Windows.Forms;


namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// 向下填充窗体类，用于处理Excel中的向下填充操作
    /// </summary>
    /// <remarks>
    /// 此窗体提供以下功能：
    /// 1. 按上一行的值进行向下填充
    /// 2. 支持多列同时填充
    /// 3. 可选择是否对填充的单元格进行标色
    /// 4. 通过弹出对话框选择不同的填充模式
    /// </remarks>
    public partial class frm向下填充 : Form
    {
        /// <summary>
        /// 初始化向下填充窗体
        /// </summary>
        public frm向下填充()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 按上一行的值填充按钮点击事件处理
        /// </summary>
        /// <remarks>
        /// 此方法执行以下操作：
        /// 1. 弹出填充选择对话框
        /// 2. 根据用户选择的模式获取目标范围
        /// 3. 执行向下填充操作
        /// 4. 可选择性地为填充的单元格添加标色
        /// </remarks>
        void button按上一行的值填充_Click(object sender, EventArgs e)
        {
            List<Range> filledCells = []; // 存放已填充单元格的Range列表

            try
            {
                // 弹出填充选择对话框
                using (frm填充选择对话框 fillDialog = new frm填充选择对话框())
                {
                    if (fillDialog.ShowDialog() != DialogResult.OK)
                        return; // 用户取消操作

                    // 获取目标填充范围
                    Range targetRange = fillDialog.GetTargetRange();
                    if (targetRange == null)
                    {
                        MessageBox.Show("无法获取有效的填充范围", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }

                    ETLogManager.Info($"开始向下填充操作，填充模式: {fillDialog.SelectedFillMode}，目标区域: {targetRange.Address}");
                    ETExcelExtensions.SetAppFastMode();

                    // 遍历每一列进行填充
                    for (int columnIndex = 1; columnIndex <= targetRange.Columns.Count; columnIndex++)
                    {
                        dynamic currentColumn = targetRange.Columns[columnIndex];
                        object previousValue = null; // 存储上一个非空值
                        bool isMergedCell = false; // 标记是否在处理合并单元格
                        int lastFilledRow = 1; // 最后一个填充值的行索引

                        foreach (Range cell in currentColumn.Cells)
                        {
                            if (cell.EntireRow.Hidden) continue; // 跳过隐藏行

                            if (!cell.IsCellEmpty())
                            {
                                previousValue = cell.Value;
                                isMergedCell = true;
                                lastFilledRow = cell.Row;
                            }
                            else if (cell.IsCellEmpty() && isMergedCell)
                            {
                                cell.Value = previousValue ?? string.Empty;
                                filledCells.Add(cell);
                                ETLogManager.Debug($"填充单元格 {cell.Address} 的值为: {previousValue}");
                            }
                            else if (!cell.IsCellEmpty() && cell.Row != lastFilledRow + 1)
                            {
                                isMergedCell = false;
                                previousValue = string.Empty;
                            }
                        }
                    }

                    // 如果需要标色且有填充的单元格
                    if (checkBox标色.Checked && filledCells.Count > 0)
                    {
                        Range filledRange = ETExcelExtensions.UnionRanges(filledCells);
                        filledRange.Format条件格式警示色(EnumWarningColor.紫罗兰);
                        ETLogManager.Info($"已为填充的单元格添加标色，范围: {filledRange.Address}");
                    }

                    // 显示填充结果
                    if (filledCells.Count > 0)
                    {
                        MessageBox.Show($"填充完成，共填充了 {filledCells.Count} 个单元格", "填充结果",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        MessageBox.Show("没有找到需要填充的空白单元格", "填充结果",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new ETException("向下填充操作失败", "单元格填充操作", ex);
            }
            finally
            {
                ETExcelExtensions.SetAppNormalMode(true);
            }

            // 默认执行后不关闭窗体
        }

        /// <summary>
        /// 窗体加载事件处理
        /// </summary>
        void frm向下填充_Load(object sender, EventArgs e)
        {
            // 窗体加载时的初始化操作
            // 由于取消了窗体大小变化功能，这里不需要特殊处理
        }
    }
}