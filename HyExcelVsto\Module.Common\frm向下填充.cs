using ET;
using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;
using System.Windows.Forms;


namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// 向下填充窗体类，用于处理Excel中的向下填充操作
    /// </summary>
    /// <remarks>
    /// 此窗体提供以下功能：
    /// 1. 按上一行的值进行向下填充
    /// 2. 支持多列同时填充
    /// 3. 默认对填充的单元格进行标色
    /// 4. 直接在窗体中选择不同的填充模式
    /// </remarks>
    public partial class frm向下填充 : Form
    {
        /// <summary>
        /// 当前选定的单元格范围
        /// </summary>
        private Range _selectedRange;

        /// <summary>
        /// 筛选行号
        /// </summary>
        private int _filterRowNumber;

        /// <summary>
        /// 初始化向下填充窗体
        /// </summary>
        public frm向下填充()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 按上一行的值填充按钮点击事件处理
        /// </summary>
        /// <remarks>
        /// 此方法执行以下操作：
        /// 1. 根据窗体中选择的模式获取目标范围
        /// 2. 执行向下填充操作
        /// 3. 默认对填充的单元格进行标色
        /// </remarks>
        void button按上一行的值填充_Click(object sender, EventArgs e)
        {
            List<Range> filledCells = []; // 存放已填充单元格的Range列表

            try
            {
                // 获取目标填充范围
                Range targetRange = GetTargetRange();
                if (targetRange == null)
                {
                    return;
                }

                string fillMode = GetSelectedFillMode();
                ETLogManager.Info($"开始向下填充操作，填充模式: {fillMode}，目标区域: {targetRange.Address}");
                ETExcelExtensions.SetAppFastMode();

                // 遍历每一列进行填充
                for (int columnIndex = 1; columnIndex <= targetRange.Columns.Count; columnIndex++)
                {
                    dynamic currentColumn = targetRange.Columns[columnIndex];
                    object previousValue = null; // 存储上一个非空值
                    bool isMergedCell = false; // 标记是否在处理合并单元格
                    int lastFilledRow = 1; // 最后一个填充值的行索引

                    foreach (Range cell in currentColumn.Cells)
                    {
                        if (cell.EntireRow.Hidden) continue; // 跳过隐藏行

                        if (!cell.IsCellEmpty())
                        {
                            previousValue = cell.Value;
                            isMergedCell = true;
                            lastFilledRow = cell.Row;
                        }
                        else if (cell.IsCellEmpty() && isMergedCell)
                        {
                            cell.Value = previousValue ?? string.Empty;
                            filledCells.Add(cell);
                            ETLogManager.Debug($"填充单元格 {cell.Address} 的值为: {previousValue}");
                        }
                        else if (!cell.IsCellEmpty() && cell.Row != lastFilledRow + 1)
                        {
                            isMergedCell = false;
                            previousValue = string.Empty;
                        }
                    }
                }

                // 默认对填充的单元格进行标色
                if (filledCells.Count > 0)
                {
                    Range filledRange = ETExcelExtensions.UnionRanges(filledCells);
                    filledRange.Format条件格式警示色(EnumWarningColor.紫罗兰);
                    ETLogManager.Info($"已为填充的单元格添加标色，范围: {filledRange.Address}");
                }
            }
            catch (Exception ex)
            {
                throw new ETException("向下填充操作失败", "单元格填充操作", ex);
            }
            finally
            {
                ETExcelExtensions.SetAppNormalMode(true);
            }

            // 默认执行后不关闭窗体
        }

        /// <summary>
        /// 窗体加载事件处理
        /// </summary>
        void frm向下填充_Load(object sender, EventArgs e)
        {
            InitializeDialog();
        }

        /// <summary>
        /// 初始化对话框内容
        /// </summary>
        private void InitializeDialog()
        {
            try
            {
                // 获取当前选定范围
                _selectedRange = ETExcelExtensions.GetSelectionRange();
                if (_selectedRange == null)
                {
                    return;
                }

                // 获取筛选行号
                _filterRowNumber = _selectedRange.Worksheet.GetWorksheetFilterRowNumber();

                // 更新选项描述
                UpdateOptionDescriptions();
            }
            catch (Exception ex)
            {
                ETLogManager.Error("初始化向下填充窗体失败", ex);
            }
        }

        /// <summary>
        /// 更新选项描述文本
        /// </summary>
        private void UpdateOptionDescriptions()
        {
            if (_selectedRange == null) return;

            // 获取选定范围的第一个单元格
            Range firstCell = _selectedRange.Cells[1, 1];
            string cellAddress = firstCell.Address[false, false];

            // 更新选项1描述 - 显示起始单元格
            radioButton填充当前单元格下方.Text = $"填充当前下方 ({cellAddress} 开始)";

            // 更新选项2描述 - 显示筛选行信息
            if (_filterRowNumber > 0)
            {
                radioButton填充筛选行下方.Text = $"填充筛选行下方 (跳过1-{_filterRowNumber}行)";
                radioButton填充筛选行下方.Enabled = true;
                radioButton填充筛选行下方.Checked = true;
            }
            else
            {
                radioButton填充筛选行下方.Text = "填充筛选行下方 (当前无筛选)";
                radioButton填充筛选行下方.Enabled = false;
                radioButton填充当前单元格下方.Checked = true;
            }

            // 更新选项3描述 - 显示选定范围
            radioButton填充选定单元格.Text = $"填充选定单元格 ({_selectedRange.Address[false, false]})";
        }

        /// <summary>
        /// 获取选择的填充模式描述
        /// </summary>
        private string GetSelectedFillMode()
        {
            if (radioButton填充当前单元格下方.Checked)
                return "填充当前单元格下方";
            else if (radioButton填充筛选行下方.Checked)
                return "填充筛选行下方";
            else if (radioButton填充选定单元格.Checked)
                return "填充选定单元格";
            else
                return "未知模式";
        }

        /// <summary>
        /// 根据选择模式计算目标填充范围
        /// </summary>
        private Range GetTargetRange()
        {
            if (_selectedRange == null) return null;

            try
            {
                if (radioButton填充当前单元格下方.Checked)
                {
                    // 填充当前单元格下方单元格：从选定单元格开始到列底部
                    Range firstCell = _selectedRange.Cells[1, 1];
                    Range columnRange = firstCell.EntireColumn;
                    Range currentCellTargetRange = _selectedRange.Worksheet.Range[
                        _selectedRange.Worksheet.Cells[firstCell.Row, firstCell.Column],
                        _selectedRange.Worksheet.Cells[columnRange.Rows.Count, firstCell.Column + _selectedRange.Columns.Count - 1]];
                    return currentCellTargetRange.OptimizeRangeSize();
                }
                else if (radioButton填充筛选行下方.Checked)
                {
                    // 填充筛选行下方单元格：从筛选行下方开始
                    if (_filterRowNumber > 0)
                    {
                        Range filterRowTargetRange = _selectedRange.Worksheet.Range[
                            _selectedRange.Worksheet.Cells[_filterRowNumber + 1, _selectedRange.Column],
                            _selectedRange.Worksheet.Cells[_selectedRange.Worksheet.UsedRange.Rows.Count, _selectedRange.Column + _selectedRange.Columns.Count - 1]];
                        return filterRowTargetRange.OptimizeRangeSize();
                    }
                    return _selectedRange.OptimizeRangeSize();
                }
                else if (radioButton填充选定单元格.Checked)
                {
                    // 填充选定单元格：仅填充用户选定的范围
                    return _selectedRange.OptimizeRangeSize();
                }

                return null;
            }
            catch (Exception ex)
            {
                ETLogManager.Error("计算目标填充范围失败", ex);
                return null;
            }
        }
    }
}