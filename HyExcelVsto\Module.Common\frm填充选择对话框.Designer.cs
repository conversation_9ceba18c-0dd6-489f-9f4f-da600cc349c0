namespace HyExcelVsto.Module.Common
{
    partial class frm填充选择对话框
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.radioButton填充选定单元格 = new System.Windows.Forms.RadioButton();
            this.radioButton填充筛选行下方 = new System.Windows.Forms.RadioButton();
            this.radioButton填充当前单元格下方 = new System.Windows.Forms.RadioButton();
            this.button确定 = new System.Windows.Forms.Button();
            this.button取消 = new System.Windows.Forms.Button();
            this.SuspendLayout();
            //
            // radioButton填充选定单元格
            //
            this.radioButton填充选定单元格.AutoSize = true;
            this.radioButton填充选定单元格.Location = new System.Drawing.Point(8, 55);
            this.radioButton填充选定单元格.Name = "radioButton填充选定单元格";
            this.radioButton填充选定单元格.Size = new System.Drawing.Size(200, 16);
            this.radioButton填充选定单元格.TabIndex = 2;
            this.radioButton填充选定单元格.Text = "填充选定单元格";
            this.radioButton填充选定单元格.UseVisualStyleBackColor = true;
            //
            // radioButton填充筛选行下方
            //
            this.radioButton填充筛选行下方.AutoSize = true;
            this.radioButton填充筛选行下方.Checked = true;
            this.radioButton填充筛选行下方.Location = new System.Drawing.Point(8, 35);
            this.radioButton填充筛选行下方.Name = "radioButton填充筛选行下方";
            this.radioButton填充筛选行下方.Size = new System.Drawing.Size(200, 16);
            this.radioButton填充筛选行下方.TabIndex = 1;
            this.radioButton填充筛选行下方.TabStop = true;
            this.radioButton填充筛选行下方.Text = "填充筛选行下方";
            this.radioButton填充筛选行下方.UseVisualStyleBackColor = true;
            //
            // radioButton填充当前单元格下方
            //
            this.radioButton填充当前单元格下方.AutoSize = true;
            this.radioButton填充当前单元格下方.Location = new System.Drawing.Point(8, 15);
            this.radioButton填充当前单元格下方.Name = "radioButton填充当前单元格下方";
            this.radioButton填充当前单元格下方.Size = new System.Drawing.Size(200, 16);
            this.radioButton填充当前单元格下方.TabIndex = 0;
            this.radioButton填充当前单元格下方.Text = "填充当前下方";
            this.radioButton填充当前单元格下方.UseVisualStyleBackColor = true;
            //
            // button确定
            //
            this.button确定.Location = new System.Drawing.Point(140, 80);
            this.button确定.Name = "button确定";
            this.button确定.Size = new System.Drawing.Size(50, 23);
            this.button确定.TabIndex = 3;
            this.button确定.Text = "确定";
            this.button确定.UseVisualStyleBackColor = true;
            this.button确定.Click += new System.EventHandler(this.button确定_Click);
            //
            // button取消
            //
            this.button取消.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.button取消.Location = new System.Drawing.Point(195, 80);
            this.button取消.Name = "button取消";
            this.button取消.Size = new System.Drawing.Size(50, 23);
            this.button取消.TabIndex = 4;
            this.button取消.Text = "取消";
            this.button取消.UseVisualStyleBackColor = true;
            this.button取消.Click += new System.EventHandler(this.button取消_Click);
            //
            // frm填充选择对话框
            //
            this.AcceptButton = this.button确定;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.CancelButton = this.button取消;
            this.ClientSize = new System.Drawing.Size(255, 110);
            this.Controls.Add(this.button取消);
            this.Controls.Add(this.button确定);
            this.Controls.Add(this.radioButton填充选定单元格);
            this.Controls.Add(this.radioButton填充筛选行下方);
            this.Controls.Add(this.radioButton填充当前单元格下方);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "frm填充选择对话框";
            this.ShowInTaskbar = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "填充模式";
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.RadioButton radioButton填充选定单元格;
        private System.Windows.Forms.RadioButton radioButton填充筛选行下方;
        private System.Windows.Forms.RadioButton radioButton填充当前单元格下方;
        private System.Windows.Forms.Button button确定;
        private System.Windows.Forms.Button button取消;
    }
}
