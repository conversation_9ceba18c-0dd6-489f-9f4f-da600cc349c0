namespace HyExcelVsto.Module.Common
{
    partial class frm填充选择对话框
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.groupBox填充选项 = new System.Windows.Forms.GroupBox();
            this.radioButton填充选定单元格 = new System.Windows.Forms.RadioButton();
            this.radioButton填充筛选行下方 = new System.Windows.Forms.RadioButton();
            this.radioButton填充当前单元格下方 = new System.Windows.Forms.RadioButton();
            this.button确定 = new System.Windows.Forms.Button();
            this.button取消 = new System.Windows.Forms.Button();
            this.label说明 = new System.Windows.Forms.Label();
            this.groupBox填充选项.SuspendLayout();
            this.SuspendLayout();
            // 
            // groupBox填充选项
            // 
            this.groupBox填充选项.Controls.Add(this.radioButton填充选定单元格);
            this.groupBox填充选项.Controls.Add(this.radioButton填充筛选行下方);
            this.groupBox填充选项.Controls.Add(this.radioButton填充当前单元格下方);
            this.groupBox填充选项.Location = new System.Drawing.Point(12, 35);
            this.groupBox填充选项.Name = "groupBox填充选项";
            this.groupBox填充选项.Size = new System.Drawing.Size(460, 120);
            this.groupBox填充选项.TabIndex = 0;
            this.groupBox填充选项.TabStop = false;
            this.groupBox填充选项.Text = "请选择填充模式";
            // 
            // radioButton填充选定单元格
            // 
            this.radioButton填充选定单元格.AutoSize = true;
            this.radioButton填充选定单元格.Location = new System.Drawing.Point(15, 85);
            this.radioButton填充选定单元格.Name = "radioButton填充选定单元格";
            this.radioButton填充选定单元格.Size = new System.Drawing.Size(200, 16);
            this.radioButton填充选定单元格.TabIndex = 2;
            this.radioButton填充选定单元格.Text = "填充选定单元格";
            this.radioButton填充选定单元格.UseVisualStyleBackColor = true;
            // 
            // radioButton填充筛选行下方
            // 
            this.radioButton填充筛选行下方.AutoSize = true;
            this.radioButton填充筛选行下方.Checked = true;
            this.radioButton填充筛选行下方.Location = new System.Drawing.Point(15, 55);
            this.radioButton填充筛选行下方.Name = "radioButton填充筛选行下方";
            this.radioButton填充筛选行下方.Size = new System.Drawing.Size(200, 16);
            this.radioButton填充筛选行下方.TabIndex = 1;
            this.radioButton填充筛选行下方.TabStop = true;
            this.radioButton填充筛选行下方.Text = "填充筛选行下方单元格";
            this.radioButton填充筛选行下方.UseVisualStyleBackColor = true;
            // 
            // radioButton填充当前单元格下方
            // 
            this.radioButton填充当前单元格下方.AutoSize = true;
            this.radioButton填充当前单元格下方.Location = new System.Drawing.Point(15, 25);
            this.radioButton填充当前单元格下方.Name = "radioButton填充当前单元格下方";
            this.radioButton填充当前单元格下方.Size = new System.Drawing.Size(200, 16);
            this.radioButton填充当前单元格下方.TabIndex = 0;
            this.radioButton填充当前单元格下方.Text = "填充当前单元格下方单元格";
            this.radioButton填充当前单元格下方.UseVisualStyleBackColor = true;
            // 
            // button确定
            // 
            this.button确定.Location = new System.Drawing.Point(316, 170);
            this.button确定.Name = "button确定";
            this.button确定.Size = new System.Drawing.Size(75, 28);
            this.button确定.TabIndex = 1;
            this.button确定.Text = "确定";
            this.button确定.UseVisualStyleBackColor = true;
            this.button确定.Click += new System.EventHandler(this.button确定_Click);
            // 
            // button取消
            // 
            this.button取消.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.button取消.Location = new System.Drawing.Point(397, 170);
            this.button取消.Name = "button取消";
            this.button取消.Size = new System.Drawing.Size(75, 28);
            this.button取消.TabIndex = 2;
            this.button取消.Text = "取消";
            this.button取消.UseVisualStyleBackColor = true;
            this.button取消.Click += new System.EventHandler(this.button取消_Click);
            // 
            // label说明
            // 
            this.label说明.AutoSize = true;
            this.label说明.ForeColor = System.Drawing.Color.Blue;
            this.label说明.Location = new System.Drawing.Point(12, 9);
            this.label说明.Name = "label说明";
            this.label说明.Size = new System.Drawing.Size(389, 12);
            this.label说明.TabIndex = 3;
            this.label说明.Text = "根据您的需要选择填充模式，系统将按上一行的值填充空白单元格：";
            // 
            // frm填充选择对话框
            // 
            this.AcceptButton = this.button确定;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.CancelButton = this.button取消;
            this.ClientSize = new System.Drawing.Size(484, 210);
            this.Controls.Add(this.label说明);
            this.Controls.Add(this.button取消);
            this.Controls.Add(this.button确定);
            this.Controls.Add(this.groupBox填充选项);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "frm填充选择对话框";
            this.ShowInTaskbar = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "选择填充模式";
            this.groupBox填充选项.ResumeLayout(false);
            this.groupBox填充选项.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBox填充选项;
        private System.Windows.Forms.RadioButton radioButton填充选定单元格;
        private System.Windows.Forms.RadioButton radioButton填充筛选行下方;
        private System.Windows.Forms.RadioButton radioButton填充当前单元格下方;
        private System.Windows.Forms.Button button确定;
        private System.Windows.Forms.Button button取消;
        private System.Windows.Forms.Label label说明;
    }
}
